# 智能体消息发布功能

## 概述

本文档介绍了UI自动化测试系统中智能体消息发布功能的实现和使用方法。该功能允许智能体向运行时环境发布消息，实现智能体间的通信和状态同步。

## 核心组件

### 1. BaseAgent 基础智能体类

`BaseAgent` 类提供了统一的消息发布接口：

```python
from app.core.agents.base import BaseAgent
from app.core.types import AgentPlatform

class MyAgent(BaseAgent):
    def __init__(self):
        super().__init__("my_agent", "我的智能体", AgentPlatform.WEB)
```

### 2. RuntimeManager 运行时管理器

`RuntimeManager` 负责管理全局运行时实例：

```python
from app.core.runtime.manager import RuntimeManager

# 获取运行时管理器实例
manager = RuntimeManager.get_instance()

# 初始化运行时
runtime = manager.initialize_runtime("single_threaded")

# 启动运行时
manager.start_runtime()
```

### 3. 消息类型

系统支持多种消息类型：

- **StreamMessage**: 流式消息，用于实时状态更新
- **进度消息**: 显示任务执行进度
- **成功消息**: 任务完成通知
- **警告消息**: 警告信息
- **错误消息**: 错误信息
- **信息消息**: 一般信息

## 使用方法

### 1. 基本消息发送

```python
# 发送信息消息
await agent.send_info("开始处理任务")

# 发送进度消息
await agent.send_progress("正在处理...", 50)  # 50% 进度

# 发送成功消息
await agent.send_success("任务完成", {"result": "success"})

# 发送警告消息
await agent.send_warning("检测到潜在问题")

# 发送错误消息
await agent.send_error("处理失败", is_final=True)
```

### 2. 自定义消息发布

```python
from app.core.messages.base import StreamMessage
from autogen_core import TopicId

# 创建自定义消息
message = StreamMessage(
    type="custom_event",
    source=agent.agent_name,
    content="自定义事件发生",
    region="process",
    is_final=False,
    result={"data": "custom_data"},
    message_id="custom-001",
    platform="web"
)

# 发布到指定主题
topic_id = TopicId(type="custom_topic", source=agent.id.key)
await agent.publish_message(message, topic_id)
```

### 3. 运行时设置

```python
# 方法1: 通过智能体设置
agent.set_runtime(runtime)

# 方法2: 使用全局运行时管理器
from app.core.runtime import initialize_global_runtime, start_global_runtime

runtime = initialize_global_runtime()
start_global_runtime()
```

### 4. 异常处理

```python
try:
    # 执行可能出错的操作
    result = await some_operation()
except Exception as e:
    # 自动发送错误消息并记录日志
    await agent.handle_exception("some_operation", e, send_error_message=True)
```

### 5. 性能监控

```python
# 开始性能监控
monitor_id = agent.start_performance_monitoring("data_processing")

try:
    # 执行需要监控的操作
    await process_data()
finally:
    # 结束监控并获取结果
    metrics = agent.end_performance_monitoring(monitor_id, log_result=True)
    print(f"操作耗时: {metrics['duration']:.2f}秒")
```

## 完整示例

```python
import asyncio
from app.core.agents.base import BaseAgent
from app.core.types import AgentPlatform
from app.core.runtime.manager import RuntimeManager

class DataProcessorAgent(BaseAgent):
    def __init__(self):
        super().__init__("data_processor", "数据处理智能体", AgentPlatform.WEB)
        # 设置智能体ID
        from types import SimpleNamespace
        self.id = SimpleNamespace(key="data_processor")
    
    async def process_data(self, data):
        """处理数据的主要方法"""
        try:
            # 开始性能监控
            monitor_id = self.start_performance_monitoring("data_processing")
            
            # 发送开始消息
            await self.send_info(f"开始处理数据: {len(data)} 条记录")
            
            # 模拟数据处理过程
            total_steps = 4
            for step in range(1, total_steps + 1):
                await self.send_progress(
                    f"执行步骤 {step}/{total_steps}", 
                    (step / total_steps) * 100
                )
                await asyncio.sleep(0.5)  # 模拟处理时间
            
            # 处理完成
            result = {"processed_count": len(data), "status": "success"}
            await self.send_success("数据处理完成", result)
            
            # 结束性能监控
            metrics = self.end_performance_monitoring(monitor_id)
            
            return result
            
        except Exception as e:
            await self.handle_exception("process_data", e, True)
            return None

async def main():
    # 1. 初始化运行时
    manager = RuntimeManager.get_instance()
    runtime = manager.initialize_runtime("single_threaded")
    manager.start_runtime()
    
    # 2. 创建智能体
    agent = DataProcessorAgent()
    agent.set_runtime(runtime)
    
    # 3. 执行任务
    test_data = [f"record_{i}" for i in range(100)]
    result = await agent.process_data(test_data)
    
    print(f"处理结果: {result}")
    
    # 4. 停止运行时
    await manager.stop_runtime()

if __name__ == "__main__":
    asyncio.run(main())
```

## 配置选项

### 运行时类型

- `single_threaded`: 单线程运行时（默认）
- `mock`: 模拟运行时（用于测试）

### 消息区域

- `MessageRegion.PROCESS`: 处理区域（默认）
- `MessageRegion.SUCCESS`: 成功区域
- `MessageRegion.WARNING`: 警告区域
- `MessageRegion.ERROR`: 错误区域
- `MessageRegion.INFO`: 信息区域

## 最佳实践

1. **运行时管理**: 在应用启动时初始化运行时，在应用关闭时停止运行时
2. **错误处理**: 使用 `handle_exception` 方法统一处理异常
3. **性能监控**: 对重要操作使用性能监控
4. **消息分类**: 根据消息重要性选择合适的消息类型
5. **资源清理**: 确保在适当时机停止运行时以释放资源

## 故障排除

### 常见问题

1. **运行时未初始化**: 确保在使用前调用 `initialize_runtime()`
2. **消息发布失败**: 检查运行时是否已启动
3. **智能体ID未设置**: 确保智能体有有效的 `id` 属性

### 调试技巧

1. 启用详细日志记录
2. 使用模拟运行时进行测试
3. 检查运行时状态和智能体配置

## 扩展功能

系统支持以下扩展：

1. **自定义消息类型**: 继承 `StreamMessage` 创建自定义消息
2. **消息订阅**: 实现消息订阅者来处理特定类型的消息
3. **消息路由**: 配置消息路由规则
4. **消息持久化**: 将消息保存到数据库或文件
