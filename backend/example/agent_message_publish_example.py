"""
智能体消息发布示例
演示如何使用BaseAgent的消息发布功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.agents.base import BaseAgent
from app.core.types import AgentPlatform, MessageRegion, TopicTypes
from app.core.messages.base import StreamMessage
from app.core.runtime.manager import RuntimeManager
from autogen_core import TopicId
from loguru import logger


class ExampleAgent(BaseAgent):
    """示例智能体类"""
    
    def __init__(self, agent_id: str, agent_name: str):
        super().__init__(agent_id, agent_name, AgentPlatform.WEB)
        # 模拟设置智能体ID
        from types import SimpleNamespace
        self.id = SimpleNamespace(key=agent_id)
    
    async def process_task(self, task_description: str):
        """处理任务并发送消息"""
        try:
            # 发送开始消息
            await self.send_info(f"开始处理任务: {task_description}")
            
            # 模拟处理过程
            await self.send_progress("正在分析任务...", 25)
            await asyncio.sleep(1)
            
            await self.send_progress("正在执行任务...", 50)
            await asyncio.sleep(1)
            
            await self.send_progress("正在生成结果...", 75)
            await asyncio.sleep(1)
            
            # 发送成功消息
            result = {"task": task_description, "status": "completed", "result": "任务执行成功"}
            await self.send_success("任务处理完成", result)
            
            # 发布自定义消息到特定主题
            custom_message = StreamMessage(
                type="task_completed",
                source=self.agent_name,
                content=f"任务 '{task_description}' 已完成",
                region=MessageRegion.SUCCESS.value,
                is_final=True,
                result=result,
                message_id=f"{self.id.key}-task-completed",
                platform=self.platform.value
            )
            
            # 发布到自定义主题
            await self.publish_message(
                custom_message,
                topic_id=TopicId(type="task_completion", source=self.id.key)
            )
            
        except Exception as e:
            await self.send_error(f"任务处理失败: {str(e)}")


class MessageSubscriber:
    """消息订阅者示例"""
    
    def __init__(self, name: str):
        self.name = name
    
    async def handle_stream_message(self, message: StreamMessage):
        """处理流消息"""
        logger.info(f"[{self.name}] 收到消息: {message.type} - {message.content}")
    
    async def handle_task_completion(self, message: StreamMessage):
        """处理任务完成消息"""
        logger.info(f"[{self.name}] 任务完成通知: {message.content}")
        if message.result:
            logger.info(f"[{self.name}] 任务结果: {message.result}")


async def main():
    """主函数 - 演示消息发布功能"""
    logger.info("开始消息发布示例")
    
    # 1. 初始化运行时管理器
    runtime_manager = RuntimeManager.get_instance()
    runtime = runtime_manager.initialize_runtime("single_threaded")
    
    # 2. 启动运行时
    if runtime_manager.start_runtime():
        logger.info("运行时启动成功")
    else:
        logger.error("运行时启动失败")
        return
    
    # 3. 创建示例智能体
    agent = ExampleAgent("example_agent_001", "示例智能体")
    
    # 4. 设置智能体的运行时实例
    agent.set_runtime(runtime)
    
    # 5. 创建消息订阅者
    subscriber = MessageSubscriber("消息订阅者")
    
    # 6. 执行任务并发送消息
    tasks = [
        "分析网页UI结构",
        "生成测试用例",
        "执行自动化测试"
    ]
    
    for i, task in enumerate(tasks, 1):
        logger.info(f"\n=== 执行任务 {i}: {task} ===")
        await agent.process_task(task)
        await asyncio.sleep(0.5)  # 短暂延迟
    
    # 7. 停止运行时
    logger.info("\n=== 停止运行时 ===")
    await runtime_manager.stop_runtime()
    
    logger.info("消息发布示例完成")


async def test_message_types():
    """测试不同类型的消息发送"""
    logger.info("\n=== 测试不同类型的消息 ===")
    
    # 初始化运行时
    runtime_manager = RuntimeManager.get_instance()
    runtime = runtime_manager.initialize_runtime("single_threaded")
    runtime_manager.start_runtime()
    
    # 创建测试智能体
    agent = ExampleAgent("test_agent_001", "测试智能体")
    agent.set_runtime(runtime)
    
    # 测试各种消息类型
    await agent.send_info("这是一条信息消息")
    await agent.send_warning("这是一条警告消息")
    await agent.send_progress("处理进度", 60)
    await agent.send_success("操作成功", {"data": "test"})
    
    # 测试错误处理
    try:
        raise ValueError("这是一个测试异常")
    except Exception as e:
        await agent.handle_exception("test_function", e, True)
    
    # 停止运行时
    await runtime_manager.stop_runtime()


if __name__ == "__main__":
    # 运行主示例
    asyncio.run(main())
    
    # 运行消息类型测试
    asyncio.run(test_message_types())
