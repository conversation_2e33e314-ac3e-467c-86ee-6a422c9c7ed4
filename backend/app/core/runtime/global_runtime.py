"""
UI自动化测试系统 - 全局运行时实例
提供全局运行时实例的访问接口，支持向后兼容
"""
from typing import Optional, Any
from loguru import logger

# 全局运行时实例
_global_runtime: Optional[Any] = None


def set_runtime(runtime: Any):
    """设置全局运行时实例
    
    Args:
        runtime: 运行时实例
    """
    global _global_runtime
    _global_runtime = runtime
    logger.info("全局运行时实例已设置")


def get_runtime() -> Optional[Any]:
    """获取全局运行时实例
    
    Returns:
        运行时实例或None
    """
    return _global_runtime


def clear_runtime():
    """清除全局运行时实例"""
    global _global_runtime
    _global_runtime = None
    logger.info("全局运行时实例已清除")


def is_runtime_available() -> bool:
    """检查运行时实例是否可用
    
    Returns:
        True如果运行时可用，否则False
    """
    return _global_runtime is not None
