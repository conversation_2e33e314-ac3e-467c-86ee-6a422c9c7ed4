"""
UI自动化测试系统 - 运行时管理器
管理全局运行时实例，提供智能体消息发布支持
"""
import asyncio
from typing import Optional, Dict, Any
from threading import Lock
from loguru import logger

try:
    from autogen_core import SingleThreadedAgentRuntime, TopicId
    AUTOGEN_AVAILABLE = True
except ImportError:
    AUTOGEN_AVAILABLE = False
    logger.warning("AutoGen Core 不可用，使用模拟运行时")


class MockRuntime:
    """模拟运行时类，用于在AutoGen不可用时提供基本功能"""
    
    def __init__(self):
        self.started = False
        self.message_handlers = {}
        
    def start(self):
        """启动模拟运行时"""
        self.started = True
        logger.info("模拟运行时已启动")
    
    async def stop_when_idle(self):
        """停止模拟运行时"""
        self.started = False
        logger.info("模拟运行时已停止")
    
    async def publish_message(self, message, topic_id):
        """发布消息（模拟实现）"""
        logger.debug(f"模拟发布消息到主题: {topic_id.type if hasattr(topic_id, 'type') else topic_id}")
        # 这里可以添加消息队列或事件总线的实现
        # 目前只是记录日志
        
    async def send_message(self, message, agent_id):
        """发送消息给指定智能体（模拟实现）"""
        logger.debug(f"模拟发送消息给智能体: {agent_id}")


class RuntimeManager:
    """运行时管理器 - 单例模式"""
    
    _instance: Optional['RuntimeManager'] = None
    _lock = Lock()
    
    def __init__(self):
        """初始化运行时管理器"""
        self._runtime: Optional[Any] = None
        self._is_started = False
        
    @classmethod
    def get_instance(cls) -> 'RuntimeManager':
        """获取运行时管理器单例实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
                    logger.info("运行时管理器实例已创建")
        return cls._instance
    
    def initialize_runtime(self, runtime_type: str = "single_threaded") -> Any:
        """初始化运行时实例
        
        Args:
            runtime_type: 运行时类型，默认为单线程
            
        Returns:
            运行时实例
        """
        if self._runtime is not None:
            logger.warning("运行时实例已存在，跳过初始化")
            return self._runtime
        
        try:
            if AUTOGEN_AVAILABLE and runtime_type == "single_threaded":
                self._runtime = SingleThreadedAgentRuntime()
                logger.info("AutoGen单线程运行时已初始化")
            else:
                self._runtime = MockRuntime()
                logger.info("模拟运行时已初始化")
                
        except Exception as e:
            logger.error(f"运行时初始化失败: {str(e)}")
            # 回退到模拟运行时
            self._runtime = MockRuntime()
            logger.info("已回退到模拟运行时")
        
        return self._runtime
    
    def get_runtime(self) -> Optional[Any]:
        """获取当前运行时实例"""
        return self._runtime
    
    def set_runtime(self, runtime: Any):
        """设置运行时实例
        
        Args:
            runtime: 运行时实例
        """
        self._runtime = runtime
        logger.info("运行时实例已设置")
    
    def start_runtime(self):
        """启动运行时"""
        if self._runtime is None:
            logger.error("运行时实例未初始化")
            return False
        
        try:
            if hasattr(self._runtime, 'start'):
                self._runtime.start()
            self._is_started = True
            logger.info("运行时已启动")
            return True
        except Exception as e:
            logger.error(f"运行时启动失败: {str(e)}")
            return False
    
    async def stop_runtime(self):
        """停止运行时"""
        if self._runtime is None:
            logger.warning("运行时实例未初始化")
            return
        
        try:
            if hasattr(self._runtime, 'stop_when_idle'):
                await self._runtime.stop_when_idle()
            self._is_started = False
            logger.info("运行时已停止")
        except Exception as e:
            logger.error(f"运行时停止失败: {str(e)}")
    
    def is_started(self) -> bool:
        """检查运行时是否已启动"""
        return self._is_started
    
    async def publish_message(self, message, topic_id):
        """发布消息到指定主题
        
        Args:
            message: 消息对象
            topic_id: 主题ID
        """
        if self._runtime is None:
            logger.error("运行时实例未初始化，无法发布消息")
            return
        
        try:
            await self._runtime.publish_message(message, topic_id=topic_id)
        except Exception as e:
            logger.error(f"消息发布失败: {str(e)}")
    
    async def send_message(self, message, agent_id):
        """发送消息给指定智能体
        
        Args:
            message: 消息对象
            agent_id: 智能体ID
        """
        if self._runtime is None:
            logger.error("运行时实例未初始化，无法发送消息")
            return
        
        try:
            await self._runtime.send_message(message, agent_id)
        except Exception as e:
            logger.error(f"消息发送失败: {str(e)}")
    
    def reset(self):
        """重置运行时管理器"""
        self._runtime = None
        self._is_started = False
        logger.info("运行时管理器已重置")


# 全局运行时管理器实例
runtime_manager = RuntimeManager.get_instance()


def get_runtime_manager() -> RuntimeManager:
    """获取全局运行时管理器实例"""
    return runtime_manager


def get_runtime():
    """获取当前运行时实例（便捷函数）"""
    return runtime_manager.get_runtime()


def initialize_global_runtime(runtime_type: str = "single_threaded"):
    """初始化全局运行时（便捷函数）"""
    return runtime_manager.initialize_runtime(runtime_type)


def start_global_runtime():
    """启动全局运行时（便捷函数）"""
    return runtime_manager.start_runtime()


async def stop_global_runtime():
    """停止全局运行时（便捷函数）"""
    await runtime_manager.stop_runtime()
