"""
UI自动化测试系统 - 运行时模块
提供智能体运行时环境管理和消息发布支持
"""

from .manager import (
    RuntimeManager,
    MockRuntime,
    get_runtime_manager,
    get_runtime,
    initialize_global_runtime,
    start_global_runtime,
    stop_global_runtime
)

__all__ = [
    'RuntimeManager',
    'MockRuntime',
    'get_runtime_manager',
    'get_runtime',
    'initialize_global_runtime',
    'start_global_runtime',
    'stop_global_runtime'
]
