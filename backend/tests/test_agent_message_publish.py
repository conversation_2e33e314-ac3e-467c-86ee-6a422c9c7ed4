"""
智能体消息发布功能测试
"""
import pytest
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.agents.base import BaseAgent
from app.core.types import AgentPlatform, MessageRegion
from app.core.runtime.manager import RuntimeManager
from app.core.messages.base import StreamMessage
from autogen_core import TopicId


class TestAgent(BaseAgent):
    """测试智能体类"""
    
    def __init__(self, agent_id: str = "test_agent", agent_name: str = "测试智能体"):
        super().__init__(agent_id, agent_name, AgentPlatform.WEB)
        # 模拟设置智能体ID
        from types import SimpleNamespace
        self.id = SimpleNamespace(key=agent_id)


class TestAgentMessagePublish:
    """智能体消息发布测试类"""
    
    @pytest.fixture
    def runtime_manager(self):
        """运行时管理器fixture"""
        manager = RuntimeManager.get_instance()
        manager.reset()  # 重置状态
        return manager
    
    @pytest.fixture
    def test_agent(self):
        """测试智能体fixture"""
        return TestAgent()
    
    def test_runtime_manager_singleton(self, runtime_manager):
        """测试运行时管理器单例模式"""
        manager2 = RuntimeManager.get_instance()
        assert runtime_manager is manager2
    
    def test_runtime_initialization(self, runtime_manager):
        """测试运行时初始化"""
        runtime = runtime_manager.initialize_runtime("single_threaded")
        assert runtime is not None
        assert runtime_manager.get_runtime() is runtime
    
    @pytest.mark.asyncio
    async def test_runtime_start_stop(self, runtime_manager):
        """测试运行时启动和停止"""
        runtime = runtime_manager.initialize_runtime("single_threaded")

        # 测试启动
        assert runtime_manager.start_runtime() is True
        assert runtime_manager.is_started() is True

        # 测试停止
        await runtime_manager.stop_runtime()
        assert runtime_manager.is_started() is False
    
    def test_agent_runtime_setting(self, test_agent, runtime_manager):
        """测试智能体运行时设置"""
        runtime = runtime_manager.initialize_runtime("single_threaded")
        
        # 设置运行时
        test_agent.set_runtime(runtime)
        assert hasattr(test_agent, '_runtime')
        assert test_agent._runtime is runtime
        
        # 获取运行时实例
        retrieved_runtime = test_agent._get_runtime_instance()
        assert retrieved_runtime is runtime
    
    @pytest.mark.asyncio
    async def test_message_publishing(self, test_agent, runtime_manager):
        """测试消息发布功能"""
        runtime = runtime_manager.initialize_runtime("single_threaded")
        runtime_manager.start_runtime()
        test_agent.set_runtime(runtime)
        
        # 创建测试消息
        message = StreamMessage(
            type="test",
            source=test_agent.agent_name,
            content="测试消息",
            region=MessageRegion.PROCESS.value,
            is_final=False,
            message_id="test-message-001",
            platform=test_agent.platform.value
        )
        
        # 发布消息（不应该抛出异常）
        topic_id = TopicId(type="test_topic", source=test_agent.id.key)
        await test_agent.publish_message(message, topic_id)
        
        await runtime_manager.stop_runtime()
    
    @pytest.mark.asyncio
    async def test_send_message_methods(self, test_agent, runtime_manager):
        """测试各种发送消息方法"""
        runtime = runtime_manager.initialize_runtime("single_threaded")
        runtime_manager.start_runtime()
        test_agent.set_runtime(runtime)
        
        # 测试各种消息发送方法
        await test_agent.send_info("信息消息")
        await test_agent.send_warning("警告消息")
        await test_agent.send_progress("进度消息", 50)
        await test_agent.send_success("成功消息", {"result": "ok"})
        await test_agent.send_error("错误消息", False)
        
        await runtime_manager.stop_runtime()
    
    @pytest.mark.asyncio
    async def test_exception_handling(self, test_agent, runtime_manager):
        """测试异常处理"""
        runtime = runtime_manager.initialize_runtime("single_threaded")
        runtime_manager.start_runtime()
        test_agent.set_runtime(runtime)
        
        # 测试异常处理
        test_exception = ValueError("测试异常")
        await test_agent.handle_exception("test_function", test_exception, True)
        
        await runtime_manager.stop_runtime()
    
    def test_performance_monitoring(self, test_agent):
        """测试性能监控功能"""
        # 开始监控
        monitor_id = test_agent.start_performance_monitoring("test_operation")
        assert monitor_id is not None
        assert monitor_id in test_agent.performance_metrics
        
        # 结束监控
        import time
        time.sleep(0.1)  # 短暂延迟
        result = test_agent.end_performance_monitoring(monitor_id)
        
        assert result is not None
        assert "duration" in result
        assert result["duration"] > 0
        assert monitor_id not in test_agent.performance_metrics
    
    @pytest.mark.asyncio
    async def test_publish_without_runtime(self, test_agent):
        """测试没有运行时时的消息发布"""
        # 不设置运行时，直接发布消息
        message = StreamMessage(
            type="test",
            source=test_agent.agent_name,
            content="测试消息",
            region=MessageRegion.PROCESS.value,
            message_id="test-message-002",
            platform=test_agent.platform.value
        )
        
        topic_id = TopicId(type="test_topic", source=test_agent.id.key)
        
        # 应该不抛出异常，只是记录警告
        await test_agent.publish_message(message, topic_id)


if __name__ == "__main__":
    # 运行简单测试
    async def run_simple_test():
        test_instance = TestAgentMessagePublish()
        runtime_manager = RuntimeManager.get_instance()
        runtime_manager.reset()
        
        test_agent = TestAgent()
        
        print("测试运行时管理器...")
        test_instance.test_runtime_manager_singleton(runtime_manager)
        test_instance.test_runtime_initialization(runtime_manager)
        await test_instance.test_runtime_start_stop(runtime_manager)
        
        print("测试智能体功能...")
        test_instance.test_agent_runtime_setting(test_agent, runtime_manager)
        await test_instance.test_message_publishing(test_agent, runtime_manager)
        await test_instance.test_send_message_methods(test_agent, runtime_manager)
        
        print("测试性能监控...")
        test_instance.test_performance_monitoring(test_agent)
        
        print("所有测试通过！")
    
    asyncio.run(run_simple_test())
